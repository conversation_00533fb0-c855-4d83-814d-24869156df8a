[{"rolname": "supabase_admin", "query": "select * from realtime.list_changes($1, $2, $3, $4)", "calls": 169496, "total_time": 611467.640344004, "prop_total_time": "95.9%"}, {"rolname": "authenticator", "query": "SELECT name FROM pg_timezone_names", "calls": 79, "total_time": 8102.905609, "prop_total_time": "1.3%"}, {"rolname": "postgres", "query": "with tables as (SELECT\n  c.oid :: int8 AS id,\n  nc.nspname AS schema,\n  c.relname AS name,\n  c.relrowsecurity AS rls_enabled,\n  c.relforcerowsecurity AS rls_forced,\n  CASE\n    WHEN c.relreplident = $1 THEN $2\n    WHEN c.relreplident = $3 THEN $4\n    WHEN c.relreplident = $5 THEN $6\n    ELSE $7\n  END AS replica_identity,\n  pg_total_relation_size(format($8, nc.nspname, c.relname)) :: int8 AS bytes,\n  pg_size_pretty(\n    pg_total_relation_size(format($9, nc.nspname, c.relname))\n  ) AS size,\n  pg_stat_get_live_tuples(c.oid) AS live_rows_estimate,\n  pg_stat_get_dead_tuples(c.oid) AS dead_rows_estimate,\n  obj_description(c.oid) AS comment,\n  coalesce(pk.primary_keys, $10) as primary_keys,\n  coalesce(\n    jsonb_agg(relationships) filter (where relationships is not null),\n    $11\n  ) as relationships\nFROM\n  pg_namespace nc\n  JOIN pg_class c ON nc.oid = c.relnamespace\n  left join (\n    select\n      table_id,\n      jsonb_agg(_pk.*) as primary_keys\n    from (\n      select\n        n.nspname as schema,\n        c.relname as table_name,\n        a.attname as name,\n        c.oid :: int8 as table_id\n      from\n        pg_index i,\n        pg_class c,\n        pg_attribute a,\n        pg_namespace n\n      where\n        i.indrelid = c.oid\n        and c.relnamespace = n.oid\n        and a.attrelid = c.oid\n        and a.attnum = any (i.indkey)\n        and i.indisprimary\n    ) as _pk\n    group by table_id\n  ) as pk\n  on pk.table_id = c.oid\n  left join (\n    select\n      c.oid :: int8 as id,\n      c.conname as constraint_name,\n      nsa.nspname as source_schema,\n      csa.relname as source_table_name,\n      sa.attname as source_column_name,\n      nta.nspname as target_table_schema,\n      cta.relname as target_table_name,\n      ta.attname as target_column_name\n    from\n      pg_constraint c\n    join (\n      pg_attribute sa\n      join pg_class csa on sa.attrelid = csa.oid\n      join pg_namespace nsa on csa.relnamespace = nsa.oid\n    ) on sa.attrelid = c.conrelid and sa.attnum = any (c.conkey)\n    join (\n      pg_attribute ta\n      join pg_class cta on ta.attrelid = cta.oid\n      join pg_namespace nta on cta.relnamespace = nta.oid\n    ) on ta.attrelid = c.confrelid and ta.attnum = any (c.confkey)\n    where\n      c.contype = $12\n  ) as relationships\n  on (relationships.source_schema = nc.nspname and relationships.source_table_name = c.relname)\n  or (relationships.target_table_schema = nc.nspname and relationships.target_table_name = c.relname)\nWHERE\n  c.relkind IN ($13, $14)\n  AND NOT pg_is_other_temp_schema(nc.oid)\n  AND (\n    pg_has_role(c.relowner, $15)\n    OR has_table_privilege(\n      c.oid,\n      $16\n    )\n    OR has_any_column_privilege(c.oid, $17)\n  )\ngroup by\n  c.oid,\n  c.relname,\n  c.relrowsecurity,\n  c.relforcerowsecurity,\n  c.relreplident,\n  nc.nspname,\n  pk.primary_keys\n)\n  , columns as (-- Adapted from information_schema.columns\n\nSELECT\n  c.oid :: int8 AS table_id,\n  nc.nspname AS schema,\n  c.relname AS table,\n  (c.oid || $18 || a.attnum) AS id,\n  a.attnum AS ordinal_position,\n  a.attname AS name,\n  CASE\n    WHEN a.atthasdef THEN pg_get_expr(ad.adbin, ad.adrelid)\n    ELSE $19\n  END AS default_value,\n  CASE\n    WHEN t.typtype = $20 THEN CASE\n      WHEN bt.typelem <> $21 :: oid\n      AND bt.typlen = $22 THEN $23\n      WHEN nbt.nspname = $24 THEN format_type(t.typbasetype, $25)\n      ELSE $26\n    END\n    ELSE CASE\n      WHEN t.typelem <> $27 :: oid\n      AND t.typlen = $28 THEN $29\n      WHEN nt.nspname = $30 THEN format_type(a.atttypid, $31)\n      ELSE $32\n    END\n  END AS data_type,\n  COALESCE(bt.typname, t.typname) AS format,\n  a.attidentity IN ($33, $34) AS is_identity,\n  CASE\n    a.attidentity\n    WHEN $35 THEN $36\n    WHEN $37 THEN $38\n    ELSE $39\n  END AS identity_generation,\n  a.attgenerated IN ($40) AS is_generated,\n  NOT (\n    a.attnotnull\n    OR t.typtype = $41 AND t.typnotnull\n  ) AS is_nullable,\n  (\n    c.relkind IN ($42, $43)\n    OR c.relkind IN ($44, $45) AND pg_column_is_updatable(c.oid, a.attnum, $46)\n  ) AS is_updatable,\n  uniques.table_id IS NOT NULL AS is_unique,\n  check_constraints.definition AS \"check\",\n  array_to_json(\n    array(\n      SELECT\n        enumlabel\n      FROM\n        pg_catalog.pg_enum enums\n      WHERE\n        enums.enumtypid = coalesce(bt.oid, t.oid)\n        OR enums.enumtypid = coalesce(bt.typelem, t.typelem)\n      ORDER BY\n        enums.enumsortorder\n    )\n  ) AS enums,\n  col_description(c.oid, a.attnum) AS comment\nFROM\n  pg_attribute a\n  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid\n  AND a.attnum = ad.adnum\n  JOIN (\n    pg_class c\n    JOIN pg_namespace nc ON c.relnamespace = nc.oid\n  ) ON a.attrelid = c.oid\n  JOIN (\n    pg_type t\n    JOIN pg_namespace nt ON t.typnamespace = nt.oid\n  ) ON a.atttypid = t.oid\n  LEFT JOIN (\n    pg_type bt\n    JOIN pg_namespace nbt ON bt.typnamespace = nbt.oid\n  ) ON t.typtype = $47\n  AND t.typbasetype = bt.oid\n  LEFT JOIN (\n    SELECT DISTINCT ON (table_id, ordinal_position)\n      conrelid AS table_id,\n      conkey[$48] AS ordinal_position\n    FROM pg_catalog.pg_constraint\n    WHERE contype = $49 AND cardinality(conkey) = $50\n  ) AS uniques ON uniques.table_id = c.oid AND uniques.ordinal_position = a.attnum\n  LEFT JOIN (\n    -- We only select the first column check\n    SELECT DISTINCT ON (table_id, ordinal_position)\n      conrelid AS table_id,\n      conkey[$51] AS ordinal_position,\n      substring(\n        pg_get_constraintdef(pg_constraint.oid, $52),\n        $53,\n        length(pg_get_constraintdef(pg_constraint.oid, $54)) - $55\n      ) AS \"definition\"\n    FROM pg_constraint\n    WHERE contype = $56 AND cardinality(conkey) = $57\n    ORDER BY table_id, ordinal_position, oid asc\n  ) AS check_constraints ON check_constraints.table_id = c.oid AND check_constraints.ordinal_position = a.attnum\nWHERE\n  NOT pg_is_other_temp_schema(nc.oid)\n  AND a.attnum > $58\n  AND NOT a.attisdropped\n  AND (c.relkind IN ($59, $60, $61, $62, $63))\n  AND (\n    pg_has_role(c.relowner, $64)\n    OR has_column_privilege(\n      c.oid,\n      a.attnum,\n      $65\n    )\n  )\n)\nselect\n  *\n  , \nCOALESCE(\n  (\n    SELECT\n      array_agg(row_to_json(columns)) FILTER (WHERE columns.table_id = tables.id)\n    FROM\n      columns\n  ),\n  $66\n) AS columns\nfrom tables where schema IN ($67)", "calls": 16, "total_time": 3247.137305, "prop_total_time": "0.5%"}, {"rolname": "authenticator", "query": "-- Recursively get the base types of domains\n  WITH\n  base_types AS (\n    WITH RECURSIVE\n    recurse AS (\n      SELECT\n        oid,\n        typbasetype,\n        COALESCE(NULLIF(typbasetype, $3), oid) AS base\n      FROM pg_type\n      UNION\n      SELECT\n        t.oid,\n        b.typbasetype,\n        COALESCE(NULLIF(b.typbasetype, $4), b.oid) AS base\n      FROM recurse t\n      JOIN pg_type b ON t.typbasetype = b.oid\n    )\n    SELECT\n      oid,\n      base\n    FROM recurse\n    WHERE typbasetype = $5\n  ),\n  arguments AS (\n    SELECT\n      oid,\n      array_agg((\n        COALESCE(name, $6), -- name\n        type::regtype::text, -- type\n        CASE type\n          WHEN $7::regtype THEN $8\n          WHEN $9::regtype THEN $10\n          WHEN $11::regtype THEN $12\n          WHEN $13::regtype THEN $14\n          ELSE type::regtype::text\n        END, -- convert types that ignore the lenth and accept any value till maximum size\n        idx <= (pronargs - pronargdefaults), -- is_required\n        COALESCE(mode = $15, $16) -- is_variadic\n      ) ORDER BY idx) AS args,\n      CASE COUNT(*) - COUNT(name) -- number of unnamed arguments\n        WHEN $17 THEN $18\n        WHEN $19 THEN (array_agg(type))[$20] IN ($21::regtype, $22::regtype, $23::regtype, $24::regtype, $25::regtype)\n        ELSE $26\n      END AS callable\n    FROM pg_proc,\n         unnest(proargnames, proargtypes, proargmodes)\n           WITH ORDINALITY AS _ (name, type, mode, idx)\n    WHERE type IS NOT NULL -- only input arguments\n    GROUP BY oid\n  )\n  SELECT\n    pn.nspname AS proc_schema,\n    p.proname AS proc_name,\n    d.description AS proc_description,\n    COALESCE(a.args, $27) AS args,\n    tn.nspname AS schema,\n    COALESCE(comp.relname, t.typname) AS name,\n    p.proretset AS rettype_is_setof,\n    (t.typtype = $28\n     -- if any TABLE, INOUT or OUT arguments present, treat as composite\n     or COALESCE(proargmodes::text[] && $29, $30)\n    ) AS rettype_is_composite,\n    bt.oid <> bt.base as rettype_is_composite_alias,\n    p.provolatile,\n    p.provariadic > $31 as hasvariadic,\n    lower((regexp_split_to_array((regexp_split_to_array(iso_config, $32))[$33], $34))[$35]) AS transaction_isolation_level,\n    coalesce(func_settings.kvs, $36) as kvs\n  FROM pg_proc p\n  LEFT JOIN arguments a ON a.oid = p.oid\n  JOIN pg_namespace pn ON pn.oid = p.pronamespace\n  JOIN base_types bt ON bt.oid = p.prorettype\n  JOIN pg_type t ON t.oid = bt.base\n  JOIN pg_namespace tn ON tn.oid = t.typnamespace\n  LEFT JOIN pg_class comp ON comp.oid = t.typrelid\n  LEFT JOIN pg_description as d ON d.objoid = p.oid\n  LEFT JOIN LATERAL unnest(proconfig) iso_config ON iso_config LIKE $37\n  LEFT JOIN LATERAL (\n    SELECT\n      array_agg(row(\n        substr(setting, $38, strpos(setting, $39) - $40),\n        substr(setting, strpos(setting, $41) + $42)\n      )) as kvs\n    FROM unnest(proconfig) setting\n    WHERE setting ~ ANY($2)\n  ) func_settings ON $43\n  WHERE t.oid <> $44::regtype AND COALESCE(a.callable, $45)\nAND prokind = $46 AND pn.nspname = ANY($1)", "calls": 79, "total_time": 1610.635766, "prop_total_time": "0.3%"}, {"rolname": "postgres", "query": "with records as (\n  select\n    c.oid::int8 as \"id\",\n    case c.relkind\n      when $1 then pg_temp.pg_get_tabledef(\n        concat(nc.nspname),\n        concat(c.relname),\n        $2,\n        $3,\n        $4\n      )\n      when $5 then concat(\n        $6, concat(nc.nspname, $7, c.relname), $8,\n        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)\n      )\n      when $11 then concat(\n        $12, concat(nc.nspname, $13, c.relname), $14,\n        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)\n      )\n      when $17 then concat($18, nc.nspname, $19, c.relname, $20)\n      when $21 then pg_temp.pg_get_tabledef(\n        concat(nc.nspname),\n        concat(c.relname),\n        $22,\n        $23,\n        $24\n      )\n    end as \"sql\"\n  from\n    pg_namespace nc\n    join pg_class c on nc.oid = c.relnamespace\n  where\n    c.relkind in ($25, $26, $27, $28, $29)\n    and not pg_is_other_temp_schema(nc.oid)\n    and (\n      pg_has_role(c.relowner, $30)\n      or has_table_privilege(\n        c.oid,\n        $31\n      )\n      or has_any_column_privilege(c.oid, $32)\n    )\n    and nc.nspname IN ($33)\n  order by c.relname asc\n  limit $34\n  offset $35\n)\nselect\n  jsonb_build_object(\n    $36, coalesce(jsonb_agg(\n      jsonb_build_object(\n        $37, r.id,\n        $38, r.sql\n      )\n    ), $39::jsonb)\n  ) \"data\"\nfrom records r", "calls": 1, "total_time": 1512.487321, "prop_total_time": "0.2%"}, {"rolname": "postgres", "query": "with records as (\n  select\n    c.oid::int8 as \"id\",\n    case c.relkind\n      when $1 then pg_temp.pg_get_tabledef(\n        concat(nc.nspname),\n        concat(c.relname),\n        $2,\n        $3,\n        $4\n      )\n      when $5 then concat(\n        $6, concat(nc.nspname, $7, c.relname), $8,\n        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)\n      )\n      when $11 then concat(\n        $12, concat(nc.nspname, $13, c.relname), $14,\n        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)\n      )\n      when $17 then concat($18, nc.nspname, $19, c.relname, $20)\n      when $21 then pg_temp.pg_get_tabledef(\n        concat(nc.nspname),\n        concat(c.relname),\n        $22,\n        $23,\n        $24\n      )\n    end as \"sql\"\n  from\n    pg_namespace nc\n    join pg_class c on nc.oid = c.relnamespace\n  where\n    c.relkind in ($25, $26, $27, $28, $29)\n    and not pg_is_other_temp_schema(nc.oid)\n    and (\n      pg_has_role(c.relowner, $30)\n      or has_table_privilege(\n        c.oid,\n        $31\n      )\n      or has_any_column_privilege(c.oid, $32)\n    )\n    and nc.nspname IN ($33)\n  order by c.relname asc\n  limit $34\n  offset $35\n)\nselect\n  jsonb_build_object(\n    $36, coalesce(jsonb_agg(\n      jsonb_build_object(\n        $37, r.id,\n        $38, r.sql\n      )\n    ), $39::jsonb)\n  ) \"data\"\nfrom records r", "calls": 1, "total_time": 1444.418228, "prop_total_time": "0.2%"}, {"rolname": "postgres", "query": "with records as (\n  select\n    c.oid::int8 as \"id\",\n    case c.relkind\n      when $1 then pg_temp.pg_get_tabledef(\n        concat(nc.nspname),\n        concat(c.relname),\n        $2,\n        $3,\n        $4\n      )\n      when $5 then concat(\n        $6, concat(nc.nspname, $7, c.relname), $8,\n        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)\n      )\n      when $11 then concat(\n        $12, concat(nc.nspname, $13, c.relname), $14,\n        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)\n      )\n      when $17 then concat($18, nc.nspname, $19, c.relname, $20)\n      when $21 then pg_temp.pg_get_tabledef(\n        concat(nc.nspname),\n        concat(c.relname),\n        $22,\n        $23,\n        $24\n      )\n    end as \"sql\"\n  from\n    pg_namespace nc\n    join pg_class c on nc.oid = c.relnamespace\n  where\n    c.relkind in ($25, $26, $27, $28, $29)\n    and not pg_is_other_temp_schema(nc.oid)\n    and (\n      pg_has_role(c.relowner, $30)\n      or has_table_privilege(\n        c.oid,\n        $31\n      )\n      or has_any_column_privilege(c.oid, $32)\n    )\n    and nc.nspname IN ($33)\n  order by c.relname asc\n  limit $34\n  offset $35\n)\nselect\n  jsonb_build_object(\n    $36, coalesce(jsonb_agg(\n      jsonb_build_object(\n        $37, r.id,\n        $38, r.sql\n      )\n    ), $39::jsonb)\n  ) \"data\"\nfrom records r", "calls": 1, "total_time": 1442.736452, "prop_total_time": "0.2%"}, {"rolname": "postgres", "query": "with records as (\n  select\n    c.oid::int8 as \"id\",\n    case c.relkind\n      when $1 then pg_temp.pg_get_tabledef(\n        concat(nc.nspname),\n        concat(c.relname),\n        $2,\n        $3,\n        $4\n      )\n      when $5 then concat(\n        $6, concat(nc.nspname, $7, c.relname), $8,\n        pg_get_viewdef(concat(nc.nspname, $9, c.relname), $10)\n      )\n      when $11 then concat(\n        $12, concat(nc.nspname, $13, c.relname), $14,\n        pg_get_viewdef(concat(nc.nspname, $15, c.relname), $16)\n      )\n      when $17 then concat($18, nc.nspname, $19, c.relname, $20)\n      when $21 then pg_temp.pg_get_tabledef(\n        concat(nc.nspname),\n        concat(c.relname),\n        $22,\n        $23,\n        $24\n      )\n    end as \"sql\"\n  from\n    pg_namespace nc\n    join pg_class c on nc.oid = c.relnamespace\n  where\n    c.relkind in ($25, $26, $27, $28, $29)\n    and not pg_is_other_temp_schema(nc.oid)\n    and (\n      pg_has_role(c.relowner, $30)\n      or has_table_privilege(\n        c.oid,\n        $31\n      )\n      or has_any_column_privilege(c.oid, $32)\n    )\n    and nc.nspname IN ($33)\n  order by c.relname asc\n  limit $34\n  offset $35\n)\nselect\n  jsonb_build_object(\n    $36, coalesce(jsonb_agg(\n      jsonb_build_object(\n        $37, r.id,\n        $38, r.sql\n      )\n    ), $39::jsonb)\n  ) \"data\"\nfrom records r", "calls": 1, "total_time": 1423.182207, "prop_total_time": "0.2%"}, {"rolname": "supabase_admin", "query": "select\n        case when not exists (\n          select $2\n          from pg_replication_slots\n          where slot_name = $1\n        )\n        then (\n          select $3 from pg_create_logical_replication_slot($1, $4, $5)\n        )\n        else $6\n        end", "calls": 60, "total_time": 1136.008129, "prop_total_time": "0.2%"}, {"rolname": "postgres", "query": "with f as (\n      \n-- CTE with sane arg_modes, arg_names, and arg_types.\n-- All three are always of the same length.\n-- All three include all args, including OUT and TABLE args.\nwith functions as (\n  select\n    *,\n    -- proargmodes is null when all arg modes are IN\n    coalesce(\n      p.proargmodes,\n      array_fill($1::text, array[cardinality(coalesce(p.proallargtypes, p.proargtypes))])\n    ) as arg_modes,\n    -- proargnames is null when all args are unnamed\n    coalesce(\n      p.proargnames,\n      array_fill($2::text, array[cardinality(coalesce(p.proallargtypes, p.proargtypes))])\n    ) as arg_names,\n    -- proallargtypes is null when all arg modes are IN\n    coalesce(p.proallargtypes, p.proargtypes) as arg_types,\n    array_cat(\n      array_fill($3, array[pronargs - pronargdefaults]),\n      array_fill($4, array[pronargdefaults])) as arg_has_defaults\n  from\n    pg_proc as p\n  where\n    p.prokind = $5\n)\nselect\n  f.oid as id,\n  n.nspname as schema,\n  f.proname as name,\n  l.lanname as language,\n  case\n    when l.lanname = $6 then $7\n    else f.prosrc\n  end as definition,\n  case\n    when l.lanname = $8 then f.prosrc\n    else pg_get_functiondef(f.oid)\n  end as complete_statement,\n  coalesce(f_args.args, $9) as args,\n  pg_get_function_arguments(f.oid) as argument_types,\n  pg_get_function_identity_arguments(f.oid) as identity_argument_types,\n  f.prorettype as return_type_id,\n  pg_get_function_result(f.oid) as return_type,\n  nullif(rt.typrelid, $10) as return_type_relation_id,\n  f.proretset as is_set_returning_function,\n  case\n    when f.provolatile = $11 then $12\n    when f.provolatile = $13 then $14\n    when f.provolatile = $15 then $16\n  end as behavior,\n  f.prosecdef as security_definer,\n  f_config.config_params as config_params\nfrom\n  functions f\n  left join pg_namespace n on f.pronamespace = n.oid\n  left join pg_language l on f.prolang = l.oid\n  left join pg_type rt on rt.oid = f.prorettype\n  left join (\n    select\n      oid,\n      jsonb_object_agg(param, value) filter (where param is not null) as config_params\n    from\n      (\n        select\n          oid,\n          (string_to_array(unnest(proconfig), $17))[$18] as param,\n          (string_to_array(unnest(proconfig), $19))[$20] as value\n        from\n          functions\n      ) as t\n    group by\n      oid\n  ) f_config on f_config.oid = f.oid\n  left join (\n    select\n      oid,\n      jsonb_agg(jsonb_build_object(\n        $21, t2.mode,\n        $22, name,\n        $23, type_id,\n        -- Cast null into false boolean\n        $24, COALESCE(has_default, $25)\n      )) as args\n    from\n      (\n        select\n          oid,\n          unnest(arg_modes) as mode,\n          unnest(arg_names) as name,\n          -- Coming from: coalesce(p.proallargtypes, p.proargtypes) postgres won't automatically assume\n          -- integer, we need to cast it to be properly parsed\n          unnest(arg_types)::int8 as type_id,\n          unnest(arg_has_defaults) as has_default\n        from\n          functions\n      ) as t1,\n      lateral (\n        select\n          case\n            when t1.mode = $26 then $27\n            when t1.mode = $28 then $29\n            when t1.mode = $30 then $31\n            when t1.mode = $32 then $33\n            else $34\n          end as mode\n      ) as t2\n    group by\n      t1.oid\n  ) f_args on f_args.oid = f.oid\n\n    )\n    select\n      f.*\n    from f\n   where schema NOT IN ($35,$36,$37)\n\n-- source: dashboard\n-- user: 4d3c2594-75de-48ee-a259-7ffd51727131\n-- date: 2025-07-22T09:38:04.950Z", "calls": 9, "total_time": 1071.315987, "prop_total_time": "0.2%"}, {"rolname": "postgres", "query": "with base_table_info as ( select c.oid::int8 as id, nc.nspname as schema, c.relname as name, c.relkind, c.relrowsecurity as rls_enabled, c.relforcerowsecurity as rls_forced, c.relreplident, c.relowner, obj_description(c.oid) as comment from pg_class c join pg_namespace nc on nc.oid = c.relnamespace where c.oid = $1 and not pg_is_other_temp_schema(nc.oid) and ( pg_has_role(c.relowner, $2) or has_table_privilege( c.oid, $3 ) or has_any_column_privilege(c.oid, $4) ) ), table_stats as ( select b.id, case when b.relreplident = $5 then $6 when b.relreplident = $7 then $8 when b.relreplident = $9 then $10 else $11 end as replica_identity, pg_total_relation_size(format($12, b.schema, b.name))::int8 as bytes, pg_size_pretty(pg_total_relation_size(format($13, b.schema, b.name))) as size, pg_stat_get_live_tuples(b.id) as live_rows_estimate, pg_stat_get_dead_tuples(b.id) as dead_rows_estimate from base_table_info b where b.relkind in ($14, $15) ), primary_keys as ( select i.indrelid as table_id, jsonb_agg(jsonb_build_object( $16, n.nspname, $17, c.relname, $18, i.indrelid::int8, $19, a.attname )) as primary_keys from pg_index i join pg_class c on i.indrelid = c.oid join pg_attribute a on (a.attrelid = c.oid and a.attnum = any(i.indkey)) join pg_namespace n on c.relnamespace = n.oid where i.indisprimary group by i.indrelid ), relationships as ( select c.conrelid as source_id, c.confrelid as target_id, jsonb_build_object( $20, c.oid::int8, $21, c.conname, $22, c.confdeltype, $23, c.confupdtype, $24, nsa.nspname, $25, csa.relname, $26, sa.attname, $27, nta.nspname, $28, cta.relname, $29, ta.attname ) as rel_info from pg_constraint c join pg_class csa on c.conrelid = csa.oid join pg_namespace nsa on csa.relnamespace = nsa.oid join pg_attribute sa on (sa.attrelid = c.conrelid and sa.attnum = any(c.conkey)) join pg_class cta on c.confrelid = cta.oid join pg_namespace nta on cta.relnamespace = nta.oid join pg_attribute ta on (ta.attrelid = c.confrelid and ta.attnum = any(c.confkey)) where c.contype = $30 ), columns as ( select a.attrelid as table_id, jsonb_agg(jsonb_build_object( $31, (a.attrelid || $32 || a.attnum), $33, c.oid::int8, $34, nc.nspname, $35, c.relname, $36, a.attnum, $37, a.attname, $38, case when a.atthasdef then pg_get_expr(ad.adbin, ad.adrelid) else $39 end, $40, case when t.typtype = $41 then case when bt.typelem <> $42::oid and bt.typlen = $43 then $44 when nbt.nspname = $45 then format_type(t.typbasetype, $46) else $47 end else case when t.typelem <> $48::oid and t.typlen = $49 then $50 when nt.nspname = $51 then format_type(a.atttypid, $52) else $53 end end, $54, case when t.typtype = $55 then case when nt.nspname <> $56 then concat(nt.nspname, $57, coalesce(bt.typname, t.typname)) else coalesce(bt.typname, t.typname) end else coalesce(bt.typname, t.typname) end, $58, a.attidentity in ($59, $60), $61, case a.attidentity when $62 then $63 when $64 then $65 else $66 end, $67, a.attgenerated in ($68), $69, not (a.attnotnull or t.typtype = $70 and t.typnotnull), $71, ( b.relkind in ($72, $73) or (b.relkind in ($74, $75) and pg_column_is_updatable(b.id, a.attnum, $76)) ), $77, uniques.table_id is not null, $78, check_constraints.definition, $79, col_description(c.oid, a.attnum), $80, coalesce( ( select jsonb_agg(e.enumlabel order by e.enumsortorder) from pg_catalog.pg_enum e where e.enumtypid = coalesce(bt.oid, t.oid) or e.enumtypid = coalesce(bt.typelem, t.typelem) ), $81::jsonb ) ) order by a.attnum) as columns from pg_attribute a join base_table_info b on a.attrelid = b.id join pg_class c on a.attrelid = c.oid join pg_namespace nc on c.relnamespace = nc.oid left join pg_attrdef ad on (a.attrelid = ad.adrelid and a.attnum = ad.adnum) join pg_type t on a.atttypid = t.oid join pg_namespace nt on t.typnamespace = nt.oid left join pg_type bt on (t.typtype = $82 and t.typbasetype = bt.oid) left join pg_namespace nbt on bt.typnamespace = nbt.oid left join ( select conrelid as table_id, conkey[$83] as ordinal_position from pg_catalog.pg_constraint where contype = $84 and cardinality(conkey) = $85 group by conrelid, conkey[1] ) as uniques on uniques.table_id = a.attrelid and uniques.ordinal_position = a.attnum left join ( select distinct on (conrelid, conkey[1]) conrelid as table_id, conkey[$86] as ordinal_position, substring( pg_get_constraintdef(oid, $87), $88, length(pg_get_constraintdef(oid, $89)) - $90 ) as definition from pg_constraint where contype = $91 and cardinality(conkey) = $92 order by conrelid, conkey[1], oid asc ) as check_constraints on check_constraints.table_id = a.attrelid and check_constraints.ordinal_position = a.attnum where a.attnum > $93 and not a.attisdropped group by a.attrelid ) select case b.relkind when $94 then jsonb_build_object( $95, b.relkind, $96, b.id, $97, b.schema, $98, b.name, $99, b.rls_enabled, $100, b.rls_forced, $101, ts.replica_identity, $102, ts.bytes, $103, ts.size, $104, ts.live_rows_estimate, $105, ts.dead_rows_estimate, $106, b.comment, $107, coalesce(pk.primary_keys, $108::jsonb), $109, coalesce( (select jsonb_agg(r.rel_info) from relationships r where r.source_id = b.id or r.target_id = b.id), $110::jsonb ), $111, coalesce(c.columns, $112::jsonb) ) when $113 then jsonb_build_object( $114, b.relkind, $115, b.id, $116, b.schema, $117, b.name, $118, b.rls_enabled, $119, b.rls_forced, $120, ts.replica_identity, $121, ts.bytes, $122, ts.size, $123, ts.live_rows_estimate, $124, ts.dead_rows_estimate, $125, b.comment, $126, coalesce(pk.primary_keys, $127::jsonb), $128, coalesce( (select jsonb_agg(r.rel_info) from relationships r where r.source_id = b.id or r.target_id = b.id), $129::jsonb ), $130, coalesce(c.columns, $131::jsonb) ) when $132 then jsonb_build_object( $133, b.relkind, $134, b.id, $135, b.schema, $136, b.name, $137, (pg_relation_is_updatable(b.id, $138) & $139) = $140, $141, b.comment, $142, coalesce(c.columns, $143::jsonb) ) when $144 then jsonb_build_object( $145, b.relkind, $146, b.id, $147, b.schema, $148, b.name, $149, $150, $151, b.comment, $152, coalesce(c.columns, $153::jsonb) ) when $154 then jsonb_build_object( $155, b.relkind, $156, b.id, $157, b.schema, $158, b.name, $159, b.comment, $160, coalesce(c.columns, $161::jsonb) ) end as entity from base_table_info b left join table_stats ts on b.id = ts.id left join primary_keys pk on b.id = pk.table_id left join columns c on b.id = c.table_id", "calls": 75, "total_time": 648.463417, "prop_total_time": "0.1%"}, {"rolname": "supabase_admin", "query": "with sub_tables as (\n        select\n        rr.entity\n        from\n        pg_publication_tables pub,\n        lateral (\n        select\n        format($7, pub.schemaname, pub.tablename)::regclass entity\n        ) rr\n        where\n        pub.pubname = $1\n        and pub.schemaname like (case $2 when $8 then $9 else $2 end)\n        and pub.tablename like (case $3 when $10 then $11 else $3 end)\n     )\n     insert into realtime.subscription as x(\n        subscription_id,\n        entity,\n        filters,\n        claims\n      )\n      select\n        $4::text::uuid,\n        sub_tables.entity,\n        $6,\n        $5\n      from\n        sub_tables\n        on conflict\n        (subscription_id, entity, filters)\n        do update set\n        claims = excluded.claims,\n        created_at = now()\n      returning\n         id", "calls": 7412, "total_time": 416.742468, "prop_total_time": "0.1%"}, {"rolname": "authenticator", "query": "WITH\n  columns AS (\n      SELECT\n          nc.nspname::name AS table_schema,\n          c.relname::name AS table_name,\n          a.attname::name AS column_name,\n          d.description AS description,\n  \n          CASE\n            WHEN (t.typbasetype != $2) AND (ad.adbin IS NULL) THEN pg_get_expr(t.typdefaultbin, $3)\n            WHEN a.attidentity  = $4 THEN format($5, quote_literal(seqsch.nspname || $6 || seqclass.relname))\n            WHEN a.attgenerated = $7 THEN $8\n            ELSE pg_get_expr(ad.adbin, ad.adrelid)::text\n          END AS column_default,\n          not (a.attnotnull OR t.typtype = $9 AND t.typnotnull) AS is_nullable,\n          CASE\n              WHEN t.typtype = $10 THEN\n              CASE\n                  WHEN nbt.nspname = $11::name THEN format_type(t.typbasetype, $12::integer)\n                  ELSE format_type(a.atttypid, a.atttypmod)\n              END\n              ELSE\n              CASE\n                  WHEN nt.nspname = $13::name THEN format_type(a.atttypid, $14::integer)\n                  ELSE format_type(a.atttypid, a.atttypmod)\n              END\n          END::text AS data_type,\n          format_type(a.atttypid, a.atttypmod)::text AS nominal_data_type,\n          information_schema._pg_char_max_length(\n              information_schema._pg_truetypid(a.*, t.*),\n              information_schema._pg_truetypmod(a.*, t.*)\n          )::integer AS character_maximum_length,\n          COALESCE(bt.oid, t.oid) AS base_type,\n          a.attnum::integer AS position\n      FROM pg_attribute a\n          LEFT JOIN pg_description AS d\n              ON d.objoid = a.attrelid and d.objsubid = a.attnum\n          LEFT JOIN pg_attrdef ad\n              ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum\n          JOIN (pg_class c JOIN pg_namespace nc ON c.relnamespace = nc.oid)\n              ON a.attrelid = c.oid\n          JOIN (pg_type t JOIN pg_namespace nt ON t.typnamespace = nt.oid)\n              ON a.atttypid = t.oid\n          LEFT JOIN (pg_type bt JOIN pg_namespace nbt ON bt.typnamespace = nbt.oid)\n              ON t.typtype = $15 AND t.typbasetype = bt.oid\n          LEFT JOIN (pg_collation co JOIN pg_namespace nco ON co.collnamespace = nco.oid)\n              ON a.attcollation = co.oid AND (nco.nspname <> $16::name OR co.collname <> $17::name)\n          LEFT JOIN pg_depend dep\n              ON dep.refobjid = a.attrelid and dep.refobjsubid = a.attnum and dep.deptype = $18\n          LEFT JOIN pg_class seqclass\n              ON seqclass.oid = dep.objid\n          LEFT JOIN pg_namespace seqsch\n              ON seqsch.oid = seqclass.relnamespace\n      WHERE\n          NOT pg_is_other_temp_schema(nc.oid)\n          AND a.attnum > $19\n          AND NOT a.attisdropped\n          AND c.relkind in ($20, $21, $22, $23, $24)\n          AND nc.nspname = ANY($1)\n  ),\n  columns_agg AS (\n    SELECT DISTINCT\n        info.table_schema AS table_schema,\n        info.table_name AS table_name,\n        array_agg(row(\n          info.column_name,\n          info.description,\n          info.is_nullable::boolean,\n          info.data_type,\n          info.nominal_data_type,\n          info.character_maximum_length,\n          info.column_default,\n          coalesce(enum_info.vals, $25)) order by info.position) as columns\n    FROM columns info\n    LEFT OUTER JOIN (\n        SELECT\n            e.enumtypid,\n            array_agg(e.enumlabel ORDER BY e.enumsortorder) AS vals\n        FROM pg_type t\n        JOIN pg_enum e ON t.oid = e.enumtypid\n        JOIN pg_namespace n ON n.oid = t.typnamespace\n        GROUP BY enumtypid\n    ) AS enum_info ON info.base_type = enum_info.enumtypid\n    WHERE info.table_schema NOT IN ($26, $27)\n    GROUP BY info.table_schema, info.table_name\n  ),\n  tbl_constraints AS (\n      SELECT\n          c.conname::name AS constraint_name,\n          nr.nspname::name AS table_schema,\n          r.relname::name AS table_name\n      FROM pg_namespace nc\n      JOIN pg_constraint c ON nc.oid = c.connamespace\n      JOIN pg_class r ON c.conrelid = r.oid\n      JOIN pg_namespace nr ON nr.oid = r.relnamespace\n      WHERE\n        r.relkind IN ($28, $29)\n        AND NOT pg_is_other_temp_schema(nr.oid)\n        AND c.contype = $30\n  ),\n  key_col_usage AS (\n      SELECT\n          ss.conname::name AS constraint_name,\n          ss.nr_nspname::name AS table_schema,\n          ss.relname::name AS table_name,\n          a.attname::name AS column_name,\n          (ss.x).n::integer AS ordinal_position,\n          CASE\n              WHEN ss.contype = $31 THEN information_schema._pg_index_position(ss.conindid, ss.confkey[(ss.x).n])\n              ELSE $32::integer\n          END::integer AS position_in_unique_constraint\n      FROM pg_attribute a\n      JOIN (\n        SELECT r.oid AS roid,\n          r.relname,\n          r.relowner,\n          nc.nspname AS nc_nspname,\n          nr.nspname AS nr_nspname,\n          c.oid AS coid,\n          c.conname,\n          c.contype,\n          c.conindid,\n          c.confkey,\n          information_schema._pg_expandarray(c.conkey) AS x\n        FROM pg_namespace nr\n        JOIN pg_class r\n          ON nr.oid = r.relnamespace\n        JOIN pg_constraint c\n          ON r.oid = c.conrelid\n        JOIN pg_namespace nc\n          ON c.connamespace = nc.oid\n        WHERE\n          c.contype in ($33, $34)\n          AND r.relkind IN ($35, $36)\n          AND NOT pg_is_other_temp_schema(nr.oid)\n      ) ss ON a.attrelid = ss.roid AND a.attnum = (ss.x).x\n      WHERE\n        NOT a.attisdropped\n  ),\n  tbl_pk_cols AS (\n    SELECT\n        key_col_usage.table_schema,\n        key_col_usage.table_name,\n        array_agg(key_col_usage.column_name) as pk_cols\n    FROM\n        tbl_constraints\n    JOIN\n        key_col_usage\n    ON\n        key_col_usage.table_name = tbl_constraints.table_name AND\n        key_col_usage.table_schema = tbl_constraints.table_schema AND\n        key_col_usage.constraint_name = tbl_constraints.constraint_name\n    WHERE\n        key_col_usage.table_schema NOT IN ($37, $38)\n    GROUP BY key_col_usage.table_schema, key_col_usage.table_name\n  )\n  SELECT\n    n.nspname AS table_schema,\n    c.relname AS table_name,\n    d.description AS table_description,\n    c.relkind IN ($39,$40) as is_view,\n    (\n      c.relkind IN ($41,$42)\n      OR (\n        c.relkind in ($43,$44)\n        -- The function `pg_relation_is_updateable` returns a bitmask where 8\n        -- corresponds to `1 << CMD_INSERT` in the PostgreSQL source code, i.e.\n        -- it's possible to insert into the relation.\n        AND (pg_relation_is_updatable(c.oid::regclass, $45) & $46) = $47\n      )\n    ) AS insertable,\n    (\n      c.relkind IN ($48,$49)\n      OR (\n        c.relkind in ($50,$51)\n        -- CMD_UPDATE\n        AND (pg_relation_is_updatable(c.oid::regclass, $52) & $53) = $54\n      )\n    ) AS updatable,\n    (\n      c.relkind IN ($55,$56)\n      OR (\n        c.relkind in ($57,$58)\n        -- CMD_DELETE\n        AND (pg_relation_is_updatable(c.oid::regclass, $59) & $60) = $61\n      )\n    ) AS deletable,\n    coalesce(tpks.pk_cols, $62) as pk_cols,\n    coalesce(cols_agg.columns, $63) as columns\n  FROM pg_class c\n  JOIN pg_namespace n ON n.oid = c.relnamespace\n  LEFT JOIN pg_description d on d.objoid = c.oid and d.objsubid = $64\n  LEFT JOIN tbl_pk_cols tpks ON n.nspname = tpks.table_schema AND c.relname = tpks.table_name\n  LEFT JOIN columns_agg cols_agg ON n.nspname = cols_agg.table_schema AND c.relname = cols_agg.table_name\n  WHERE c.relkind IN ($65,$66,$67,$68,$69)\n  AND n.nspname NOT IN ($70, $71)  AND not c.relispartition ORDER BY table_schema, table_name", "calls": 79, "total_time": 364.018373, "prop_total_time": "0.1%"}, {"rolname": "postgres", "query": "with tables as (SELECT\n  c.oid :: int8 AS id,\n  nc.nspname AS schema,\n  c.relname AS name,\n  c.relrowsecurity AS rls_enabled,\n  c.relforcerowsecurity AS rls_forced,\n  CASE\n    WHEN c.relreplident = $1 THEN $2\n    WHEN c.relreplident = $3 THEN $4\n    WHEN c.relreplident = $5 THEN $6\n    ELSE $7\n  END AS replica_identity,\n  pg_total_relation_size(format($8, nc.nspname, c.relname)) :: int8 AS bytes,\n  pg_size_pretty(\n    pg_total_relation_size(format($9, nc.nspname, c.relname))\n  ) AS size,\n  pg_stat_get_live_tuples(c.oid) AS live_rows_estimate,\n  pg_stat_get_dead_tuples(c.oid) AS dead_rows_estimate,\n  obj_description(c.oid) AS comment,\n  coalesce(pk.primary_keys, $10) as primary_keys,\n  coalesce(\n    jsonb_agg(relationships) filter (where relationships is not null),\n    $11\n  ) as relationships\nFROM\n  pg_namespace nc\n  JOIN pg_class c ON nc.oid = c.relnamespace\n  left join (\n    select\n      table_id,\n      jsonb_agg(_pk.*) as primary_keys\n    from (\n      select\n        n.nspname as schema,\n        c.relname as table_name,\n        a.attname as name,\n        c.oid :: int8 as table_id\n      from\n        pg_index i,\n        pg_class c,\n        pg_attribute a,\n        pg_namespace n\n      where\n        i.indrelid = c.oid\n        and c.relnamespace = n.oid\n        and a.attrelid = c.oid\n        and a.attnum = any (i.indkey)\n        and i.indisprimary\n    ) as _pk\n    group by table_id\n  ) as pk\n  on pk.table_id = c.oid\n  left join (\n    select\n      c.oid :: int8 as id,\n      c.conname as constraint_name,\n      nsa.nspname as source_schema,\n      csa.relname as source_table_name,\n      sa.attname as source_column_name,\n      nta.nspname as target_table_schema,\n      cta.relname as target_table_name,\n      ta.attname as target_column_name\n    from\n      pg_constraint c\n    join (\n      pg_attribute sa\n      join pg_class csa on sa.attrelid = csa.oid\n      join pg_namespace nsa on csa.relnamespace = nsa.oid\n    ) on sa.attrelid = c.conrelid and sa.attnum = any (c.conkey)\n    join (\n      pg_attribute ta\n      join pg_class cta on ta.attrelid = cta.oid\n      join pg_namespace nta on cta.relnamespace = nta.oid\n    ) on ta.attrelid = c.confrelid and ta.attnum = any (c.confkey)\n    where\n      c.contype = $12\n  ) as relationships\n  on (relationships.source_schema = nc.nspname and relationships.source_table_name = c.relname)\n  or (relationships.target_table_schema = nc.nspname and relationships.target_table_name = c.relname)\nWHERE\n  c.relkind IN ($13, $14)\n  AND NOT pg_is_other_temp_schema(nc.oid)\n  AND (\n    pg_has_role(c.relowner, $15)\n    OR has_table_privilege(\n      c.oid,\n      $16\n    )\n    OR has_any_column_privilege(c.oid, $17)\n  )\ngroup by\n  c.oid,\n  c.relname,\n  c.relrowsecurity,\n  c.relforcerowsecurity,\n  c.relreplident,\n  nc.nspname,\n  pk.primary_keys\n)\n  \nselect\n  *\n  \nfrom tables where schema IN ($18)", "calls": 6, "total_time": 233.580282, "prop_total_time": "0.0%"}, {"rolname": "authenticator", "query": "with\n      role_setting as (\n        select r.rolname, unnest(r.rolconfig) as setting\n        from pg_auth_members m\n        join pg_roles r on r.oid = m.roleid\n        where member = current_user::regrole::oid\n      ),\n      kv_settings AS (\n        SELECT\n          rolname,\n          substr(setting, $1, strpos(setting, $2) - $3) as key,\n          lower(substr(setting, strpos(setting, $4) + $5)) as value\n        FROM role_setting\n      ),\n      iso_setting AS (\n        SELECT rolname, value\n        FROM kv_settings\n        WHERE key = $6\n      )\n      select\n        kv.rolname,\n        i.value as iso_lvl,\n        coalesce(array_agg(row(kv.key, kv.value)) filter (where key <> $7), $8) as role_settings\n      from kv_settings kv\n      join pg_settings ps on ps.name = kv.key and (ps.context = $9 or has_parameter_privilege(current_user::regrole::oid, ps.name, $10)) \n      left join iso_setting i on i.rolname = kv.rolname\n      group by kv.rolname, i.value", "calls": 79, "total_time": 165.015401, "prop_total_time": "0.0%"}, {"rolname": "authenticator", "query": "with recursive\n      pks_fks as (\n        -- pk + fk referencing col\n        select\n          contype::text as contype,\n          conname,\n          array_length(conkey, $3) as ncol,\n          conrelid as resorigtbl,\n          col as resorigcol,\n          ord\n        from pg_constraint\n        left join lateral unnest(conkey) with ordinality as _(col, ord) on $4\n        where contype IN ($5, $6)\n        union\n        -- fk referenced col\n        select\n          concat(contype, $7) as contype,\n          conname,\n          array_length(confkey, $8) as ncol,\n          confrelid,\n          col,\n          ord\n        from pg_constraint\n        left join lateral unnest(confkey) with ordinality as _(col, ord) on $9\n        where contype=$10\n      ),\n      views as (\n        select\n          c.oid       as view_id,\n          n.nspname   as view_schema,\n          c.relname   as view_name,\n          r.ev_action as view_definition\n        from pg_class c\n        join pg_namespace n on n.oid = c.relnamespace\n        join pg_rewrite r on r.ev_class = c.oid\n        where c.relkind in ($11, $12) and n.nspname = ANY($1 || $2)\n      ),\n      transform_json as (\n        select\n          view_id, view_schema, view_name,\n          -- the following formatting is without indentation on purpose\n          -- to allow simple diffs, with less whitespace noise\n          replace(\n            replace(\n            replace(\n            replace(\n            replace(\n            replace(\n            replace(\n            regexp_replace(\n            replace(\n            replace(\n            replace(\n            replace(\n            replace(\n            replace(\n            replace(\n            replace(\n            replace(\n            replace(\n            replace(\n              view_definition::text,\n            -- This conversion to json is heavily optimized for performance.\n            -- The general idea is to use as few regexp_replace() calls as possible.\n            -- Simple replace() is a lot faster, so we jump through some hoops\n            -- to be able to use regexp_replace() only once.\n            -- This has been tested against a huge schema with 250+ different views.\n            -- The unit tests do NOT reflect all possible inputs. Be careful when changing this!\n            -- -----------------------------------------------\n            -- pattern           | replacement         | flags\n            -- -----------------------------------------------\n            -- `<>` in pg_node_tree is the same as `null` in JSON, but due to very poor performance of json_typeof\n            -- we need to make this an empty array here to prevent json_array_elements from throwing an error\n            -- when the targetList is null.\n            -- We'll need to put it first, to make the node protection below work for node lists that start with\n            -- null: `(<> ...`, too. This is the case for coldefexprs, when the first column does not have a default value.\n               $13              , $14\n            -- `,` is not part of the pg_node_tree format, but used in the regex.\n            -- This removes all `,` that might be part of column names.\n            ), $15               , $16\n            -- The same applies for `{` and `}`, although those are used a lot in pg_node_tree.\n            -- We remove the escaped ones, which might be part of column names again.\n            ), $17            , $18\n            ), $19            , $20\n            -- The fields we need are formatted as json manually to protect them from the regex.\n            ), $21   , $22\n            ), $23        , $24\n            ), $25   , $26\n            ), $27   , $28\n            -- Make the regex also match the node type, e.g. `{QUERY ...`, to remove it in one pass.\n            ), $29               , $30\n            -- Protect node lists, which start with `({` or `((` from the greedy regex.\n            -- The extra `{` is removed again later.\n            ), $31              , $32\n            ), $33              , $34\n            -- This regex removes all unused fields to avoid the need to format all of them correctly.\n            -- This leads to a smaller json result as well.\n            -- Removal stops at `,` for used fields (see above) and `}` for the end of the current node.\n            -- Nesting can't be parsed correctly with a regex, so we stop at `{` as well and\n            -- add an empty key for the followig node.\n            ), $35       , $36              , $37\n            -- For performance, the regex also added those empty keys when hitting a `,` or `}`.\n            -- Those are removed next.\n            ), $38           , $39\n            ), $40           , $41\n            -- This reverses the \"node list protection\" from above.\n            ), $42              , $43\n            -- Every key above has been added with a `,` so far. The first key in an object doesn't need it.\n            ), $44              , $45\n            -- pg_node_tree has `()` around lists, but JSON uses `[]`\n            ), $46               , $47\n            ), $48               , $49\n            -- pg_node_tree has ` ` between list items, but JSON uses `,`\n            ), $50             , $51\n          )::json as view_definition\n        from views\n      ),\n      target_entries as(\n        select\n          view_id, view_schema, view_name,\n          json_array_elements(view_definition->$52->$53) as entry\n        from transform_json\n      ),\n      results as(\n        select\n          view_id, view_schema, view_name,\n          (entry->>$54)::int as view_column,\n          (entry->>$55)::oid as resorigtbl,\n          (entry->>$56)::int as resorigcol\n        from target_entries\n      ),\n      -- CYCLE detection according to PG docs: https://www.postgresql.org/docs/current/queries-with.html#QUERIES-WITH-CYCLE\n      -- Can be replaced with CYCLE clause once PG v13 is EOL.\n      recursion(view_id, view_schema, view_name, view_column, resorigtbl, resorigcol, is_cycle, path) as(\n        select\n          r.*,\n          $57,\n          ARRAY[resorigtbl]\n        from results r\n        where view_schema = ANY ($1)\n        union all\n        select\n          view.view_id,\n          view.view_schema,\n          view.view_name,\n          view.view_column,\n          tab.resorigtbl,\n          tab.resorigcol,\n          tab.resorigtbl = ANY(path),\n          path || tab.resorigtbl\n        from recursion view\n        join results tab on view.resorigtbl=tab.view_id and view.resorigcol=tab.view_column\n        where not is_cycle\n      ),\n      repeated_references as(\n        select\n          view_id,\n          view_schema,\n          view_name,\n          resorigtbl,\n          resorigcol,\n          array_agg(attname) as view_columns\n        from recursion\n        join pg_attribute vcol on vcol.attrelid = view_id and vcol.attnum = view_column\n        group by\n          view_id,\n          view_schema,\n          view_name,\n          resorigtbl,\n          resorigcol\n      )\n      select\n        sch.nspname as table_schema,\n        tbl.relname as table_name,\n        rep.view_schema,\n        rep.view_name,\n        pks_fks.conname as constraint_name,\n        pks_fks.contype as constraint_type,\n        array_agg(row(col.attname, view_columns) order by pks_fks.ord) as column_dependencies\n      from repeated_references rep\n      join pks_fks using (resorigtbl, resorigcol)\n      join pg_class tbl on tbl.oid = rep.resorigtbl\n      join pg_attribute col on col.attrelid = tbl.oid and col.attnum = rep.resorigcol\n      join pg_namespace sch on sch.oid = tbl.relnamespace\n      group by sch.nspname, tbl.relname,  rep.view_schema, rep.view_name, pks_fks.conname, pks_fks.contype, pks_fks.ncol\n      -- make sure we only return key for which all columns are referenced in the view - no partial PKs or FKs\n      having ncol = array_length(array_agg(row(col.attname, view_columns) order by pks_fks.ord), $58)", "calls": 79, "total_time": 163.101339, "prop_total_time": "0.0%"}, {"rolname": "supabase_admin", "query": "do $$\n        begin\n          if exists (\n            select 1\n            from pg_tables\n            where schemaname = 'realtime'\n              and tablename  = 'subscription'\n          )\n          then\n            delete from realtime.subscription;\n          end if;\n      end $$", "calls": 60, "total_time": 156.705333, "prop_total_time": "0.0%"}, {"rolname": "supabase_admin", "query": "SELECT t.oid, t.typname, t.typsend, t.typreceive, t.typoutput, t.typinput,\n       coalesce(d.typelem, t.typelem), coalesce(r.rngsubtype, $1), ARRAY (\n  SELECT a.atttypid\n  FROM pg_attribute AS a\n  WHERE a.attrelid = t.typrelid AND a.attnum > $2 AND NOT a.attisdropped\n  ORDER BY a.attnum\n)\n\nFROM pg_type AS t\nLEFT JOIN pg_type AS d ON t.typbasetype = d.oid\nLEFT JOIN pg_range AS r ON r.rngtypid = t.oid OR r.rngmultitypid = t.oid OR (t.typbasetype <> $3 AND r.rngtypid = t.typbasetype)\nWHERE (t.typrelid = $4)\nAND (t.typelem = $5 OR NOT EXISTS (SELECT $6 FROM pg_catalog.pg_type s WHERE s.typrelid != $7 AND s.oid = t.typelem))", "calls": 18, "total_time": 150.54295, "prop_total_time": "0.0%"}, {"rolname": "postgres", "query": "SELECT\n  e.name,\n  n.nspname AS schema,\n  e.default_version,\n  x.extversion AS installed_version,\n  e.comment\nFROM\n  pg_available_extensions() e(name, default_version, comment)\n  LEFT JOIN pg_extension x ON e.name = x.extname\n  LEFT JOIN pg_namespace n ON x.extnamespace = n.oid", "calls": 33, "total_time": 142.624253, "prop_total_time": "0.0%"}, {"rolname": "authenticator", "query": "WITH\n    pks_uniques_cols AS (\n      SELECT\n        connamespace,\n        conrelid,\n        jsonb_agg(column_info.cols) as cols\n      FROM pg_constraint\n      JOIN lateral (\n        SELECT array_agg(cols.attname order by cols.attnum) as cols\n        FROM ( select unnest(conkey) as col) _\n        JOIN pg_attribute cols on cols.attrelid = conrelid and cols.attnum = col\n      ) column_info ON $1\n      WHERE\n        contype IN ($2, $3) and\n        connamespace::regnamespace::text <> $4\n      GROUP BY connamespace, conrelid\n    )\n    SELECT\n      ns1.nspname AS table_schema,\n      tab.relname AS table_name,\n      ns2.nspname AS foreign_table_schema,\n      other.relname AS foreign_table_name,\n      (ns1.nspname, tab.relname) = (ns2.nspname, other.relname) AS is_self,\n      traint.conname  AS constraint_name,\n      column_info.cols_and_fcols,\n      (column_info.cols IN (SELECT * FROM jsonb_array_elements(pks_uqs.cols))) AS one_to_one\n    FROM pg_constraint traint\n    JOIN LATERAL (\n      SELECT\n        array_agg(row(cols.attname, refs.attname) order by ord) AS cols_and_fcols,\n        jsonb_agg(cols.attname order by cols.attnum) AS cols\n      FROM unnest(traint.conkey, traint.confkey) WITH ORDINALITY AS _(col, ref, ord)\n      JOIN pg_attribute cols ON cols.attrelid = traint.conrelid AND cols.attnum = col\n      JOIN pg_attribute refs ON refs.attrelid = traint.confrelid AND refs.attnum = ref\n    ) AS column_info ON $5\n    JOIN pg_namespace ns1 ON ns1.oid = traint.connamespace\n    JOIN pg_class tab ON tab.oid = traint.conrelid\n    JOIN pg_class other ON other.oid = traint.confrelid\n    JOIN pg_namespace ns2 ON ns2.oid = other.relnamespace\n    LEFT JOIN pks_uniques_cols pks_uqs ON pks_uqs.connamespace = traint.connamespace AND pks_uqs.conrelid = traint.conrelid\n    WHERE traint.contype = $6\n   and traint.conparentid = $7 ORDER BY traint.conrelid, traint.conname", "calls": 79, "total_time": 134.276474, "prop_total_time": "0.0%"}]