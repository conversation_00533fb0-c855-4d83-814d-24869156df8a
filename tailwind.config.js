/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./app/**/*.{js,jsx,ts,tsx}"],
  presets: [require("nativewind/preset")],
  theme: {
    extend: {
      fontFamily: {
        rubik: ["Rubik-Regular", "sans-serif"],
        "rubik-bold": ["Rubik-Bold", "sans-serif"],
        "rubik-extrabold": ["Rubik-ExtraBold", "sans-serif"],
        "rubik-medium": ["Rubik-Medium", "sans-serif"],
        "rubik-semibold": ["Rubik-SemiBold", "sans-serif"],
        "rubik-light": ["Rubik-Light", "sans-serif"],
      },
      colors: {
        primary: "#FFD700", // Golden color
        secondary: "#B8860B", // Dark Golden Rod for contrast
        accent: "#FFAA33", // A warmer accent color
        bg: "linear-gradient(135deg, #FFD700 0%, #B8860B 100%)", // Golden gradient
      },
    },
  },
  plugins: [],
};
